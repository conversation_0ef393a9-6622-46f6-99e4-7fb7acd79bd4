# AI编程学习平台部署指南 (Windows 11)

本指南将详细说明如何在Windows 11环境下部署AI编程学习平台。该平台包含一个Python Flask后端和一个Vue.js前端。

## 1. 环境准备

在开始部署之前，请确保您的Windows 11系统已安装以下软件：

### 1.1 Python 3.x

建议安装Python 3.8或更高版本。您可以从Python官方网站下载安装程序：[https://www.python.org/downloads/windows/](https://www.python.org/downloads/windows/)

安装时请务必勾选 "Add Python to PATH" 选项。

### 1.2 Node.js 和 Yarn

Node.js是运行Vue.js前端所必需的。Yarn是Node.js的包管理器，我们将使用它来管理前端依赖。

您可以从Node.js官方网站下载安装程序（其中包含了npm）：[https://nodejs.org/en/download/](https://nodejs.org/en/download/)

安装Node.js后，打开命令提示符（CMD）或PowerShell，运行以下命令安装Yarn：

```bash
npm install -g yarn
```

### 1.3 MongoDB (可选，推荐)

本项目默认使用SQLite作为数据库，但为了更好地体验和数据管理，推荐安装MongoDB。

您可以从MongoDB官方网站下载社区版安装程序：[https://www.mongodb.com/try/download/community](https://www.mongodb.com/try/download/community)

安装过程中，请选择完整安装，并确保安装MongoDB Compass（图形化界面工具）。安装完成后，MongoDB服务通常会自动启动。

## 2. 后端部署 (Python Flask)

1.  **进入后端目录**：

    打开命令提示符（CMD）或PowerShell，导航到后端项目目录：

    ```bash
    cd path\to\your\downloaded\code\ai_programming_backend
    ```

2.  **创建并激活虚拟环境**：

    ```bash
    python -m venv venv
    .\venv\Scripts\activate
    ```

3.  **安装Python依赖**：

    ```bash
    pip install -r requirements.txt
    ```

4.  **初始化数据库 (如果使用MongoDB)**：

    如果您安装了MongoDB，并且MongoDB服务正在运行，请运行以下命令初始化数据：

    ```bash
    python src\init_mongodb.py
    ```

    如果未安装MongoDB或MongoDB服务未运行，后端将自动回退到使用SQLite数据库，无需额外操作。

5.  **启动Flask应用**：

    ```bash
    python src\main.py
    ```

    您将看到类似 `* Running on http://127.0.0.1:5001` 的输出，这表示后端服务已成功启动并监听在5001端口。

## 3. 前端部署 (Vue.js)

1.  **进入前端目录**：

    打开一个新的命令提示符（CMD）或PowerShell窗口，导航到前端项目目录：

    ```bash
    cd path\to\your\downloaded\code\ai-programming-frontend
    ```

2.  **安装Node.js依赖**：

    ```bash
    yarn install
    ```

3.  **构建Vue应用**：

    ```bash
    yarn build
    ```

    这将在 `ai-programming-frontend` 目录下生成一个 `dist` 文件夹，其中包含所有构建好的前端静态文件。

4.  **复制前端文件到后端静态目录**：

    将 `dist` 文件夹中的所有内容复制到后端项目的 `ai_programming_backend\src\static` 目录中。请确保清空目标目录后再复制。

    ```bash
    xcopy /E /I /Y dist ..\ai_programming_backend\src\static
    ```

    （注意：`..\ai_programming_backend\src\static` 是相对于 `ai-programming-frontend` 目录的路径。如果您在不同的位置，请调整路径。）

## 4. 访问网站

确保后端Flask应用正在运行（参照步骤2.5）。

打开您的Web浏览器，访问以下地址：

```
http://127.0.0.1:5001
```

您应该能看到AI编程学习平台的首页。

## 5. 注意事项

*   **端口占用**：如果5001端口被占用，您可以在 `ai_programming_backend\src\main.py` 文件中修改 `app.run` 函数的 `port` 参数来更改端口。
*   **MongoDB连接**：如果MongoDB连接失败，后端会自动切换到SQLite。您可以通过查看后端启动时的控制台输出来确认是否成功连接到MongoDB。
*   **开发模式**：当前Flask应用以调试模式运行 (`debug=True`)，这在开发过程中很方便。但在生产环境中，建议使用Gunicorn等WSGI服务器来部署Flask应用，并关闭调试模式。

如果您在部署过程中遇到任何问题，请检查控制台输出的错误信息，并根据错误信息进行排查。

